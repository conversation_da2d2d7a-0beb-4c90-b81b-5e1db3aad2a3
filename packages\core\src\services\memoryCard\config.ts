import { Schema } from "koishi";
import { MemoryCardServiceConfig } from "./service";
import { CaptureEngineConfig, RefineryEngineConfig } from "./engines";

export const MemoryCardServiceConfigSchema: Schema<MemoryCardServiceConfig> = Schema.object({
    enabled: Schema.boolean()
        .default(true)
        .description("是否启用记忆卡片服务"),

    maxCardsPerUser: Schema.number()
        .default(1000)
        .min(10)
        .max(10000)
        .description("每个用户最大记忆卡片数量"),

    cleanupIntervalHours: Schema.number()
        .default(24)
        .min(1)
        .max(168)
        .description("自动清理间隔（小时）"),

    importanceDecayFactor: Schema.number()
        .default(0.95)
        .min(0.1)
        .max(1.0)
        .description("记忆卡片重要性衰减因子"),

    followUpCheckIntervalMinutes: Schema.number()
        .default(60)
        .min(5)
        .max(1440)
        .description("后续追踪任务检查间隔（分钟）"),

    captureEngine: Schema.object({
        maxMessagesPerSegment: Schema.number()
            .default(20)
            .min(5)
            .max(100)
            .description("片段最大消息数"),

        segmentTimeoutMinutes: Schema.number()
            .default(30)
            .min(5)
            .max(120)
            .description("片段超时时间（分钟）"),

        minSegmentLength: Schema.number()
            .default(3)
            .min(1)
            .max(10)
            .description("最小片段长度"),
    }).description("信息捕获引擎配置"),

    refineryEngine: Schema.object({
        autoRefineEnabled: Schema.boolean()
            .default(true)
            .description("是否启用自动提炼"),

        refineThreshold: Schema.number()
            .default(5)
            .min(1)
            .max(20)
            .description("提炼阈值（片段长度）"),

        maxImportanceScore: Schema.number()
            .default(1.0)
            .min(0.1)
            .max(1.0)
            .description("最大重要性分数"),
    }).description("记忆提炼引擎配置"),
});

export { MemoryCardServiceConfig } from "./service";
