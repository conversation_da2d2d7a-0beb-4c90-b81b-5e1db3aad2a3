import { Context, Logger } from "koishi";
import { Services } from "../types";
import {
    MemoryCard,
    UserMemory,
    generateCardId,
    TriggerCheckResult,
    isTimeTriggerActive,
    isContextTriggerActive,
    isTimeAndContextTriggerActive
} from "./index";

/**
 * 对话片段接口
 */
export interface DialogueSegment {
    /** 片段ID */
    id: string;
    /** 用户ID */
    userId: string;
    /** 消息列表 */
    messages: {
        id: string;
        content: string;
        timestamp: string;
        isFromUser: boolean;
    }[];
    /** 片段开始时间 */
    startTime: string;
    /** 片段结束时间 */
    endTime: string;
    /** 片段摘要 */
    summary?: string;
}

/**
 * 信息捕获引擎配置
 */
export interface CaptureEngineConfig {
    /** 片段最大消息数 */
    maxMessagesPerSegment: number;
    /** 片段超时时间（分钟） */
    segmentTimeoutMinutes: number;
    /** 最小片段长度 */
    minSegmentLength: number;
}

/**
 * 记忆提炼引擎配置
 */
export interface RefineryEngineConfig {
    /** 是否启用自动提炼 */
    autoRefineEnabled: boolean;
    /** 提炼阈值（片段长度） */
    refineThreshold: number;
    /** 最大重要性分数 */
    maxImportanceScore: number;
}

/**
 * 信息捕获引擎
 * 负责监听对话消息，聚合形成有意义的对话片段
 */
export class CaptureEngine {
    private logger: Logger;
    private activeSegments: Map<string, DialogueSegment> = new Map();
    private segmentTimers: Map<string, NodeJS.Timeout> = new Map();

    constructor(
        private ctx: Context,
        private config: CaptureEngineConfig
    ) {
        this.logger = ctx[Services.Logger].getLogger("[信息捕获引擎]");
    }

    /**
     * 处理新消息
     */
    async processMessage(
        userId: string,
        messageId: string,
        content: string,
        isFromUser: boolean = true
    ): Promise<DialogueSegment | null> {
        try {
            const now = new Date().toISOString();
            let segment = this.activeSegments.get(userId);

            if (!segment) {
                // 创建新片段
                segment = {
                    id: `segment_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                    userId,
                    messages: [],
                    startTime: now,
                    endTime: now
                };
                this.activeSegments.set(userId, segment);
                this.logger.debug(`为用户 ${userId} 创建新对话片段: ${segment.id}`);
            }

            // 添加消息到片段
            segment.messages.push({
                id: messageId,
                content,
                timestamp: now,
                isFromUser
            });
            segment.endTime = now;

            // 重置超时计时器
            this.resetSegmentTimer(userId, segment);

            // 检查是否需要结束片段
            if (segment.messages.length >= this.config.maxMessagesPerSegment) {
                return this.finalizeSegment(userId);
            }

            return null;
        } catch (error) {
            this.logger.error(`处理消息失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 重置片段超时计时器
     */
    private resetSegmentTimer(userId: string, segment: DialogueSegment) {
        // 清除现有计时器
        const existingTimer = this.segmentTimers.get(userId);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }

        // 设置新计时器
        const timer = setTimeout(() => {
            this.finalizeSegment(userId);
        }, this.config.segmentTimeoutMinutes * 60 * 1000);

        this.segmentTimers.set(userId, timer);
    }

    /**
     * 完成片段
     */
    private finalizeSegment(userId: string): DialogueSegment | null {
        const segment = this.activeSegments.get(userId);
        if (!segment) {
            return null;
        }

        // 检查片段长度
        if (segment.messages.length < this.config.minSegmentLength) {
            this.logger.debug(`片段 ${segment.id} 长度不足，丢弃`);
            this.cleanupSegment(userId);
            return null;
        }

        // 生成片段摘要
        segment.summary = this.generateSegmentSummary(segment);

        // 清理资源
        this.cleanupSegment(userId);

        this.logger.debug(`完成对话片段: ${segment.id}，包含 ${segment.messages.length} 条消息`);
        return segment;
    }

    /**
     * 清理片段资源
     */
    private cleanupSegment(userId: string) {
        this.activeSegments.delete(userId);

        const timer = this.segmentTimers.get(userId);
        if (timer) {
            clearTimeout(timer);
            this.segmentTimers.delete(userId);
        }
    }

    /**
     * 生成片段摘要
     */
    private generateSegmentSummary(segment: DialogueSegment): string {
        const userMessages = segment.messages.filter(m => m.isFromUser);
        const botMessages = segment.messages.filter(m => !m.isFromUser);

        return `对话片段包含 ${userMessages.length} 条用户消息和 ${botMessages.length} 条AI回复，` +
               `时间跨度: ${segment.startTime} 到 ${segment.endTime}`;
    }

    /**
     * 获取活跃片段数量
     */
    getActiveSegmentCount(): number {
        return this.activeSegments.size;
    }

    /**
     * 强制完成所有活跃片段
     */
    finalizeAllSegments(): DialogueSegment[] {
        const segments: DialogueSegment[] = [];

        for (const userId of this.activeSegments.keys()) {
            const segment = this.finalizeSegment(userId);
            if (segment) {
                segments.push(segment);
            }
        }

        return segments;
    }
}

/**
 * 记忆提炼引擎
 * 负责从对话片段中提炼出结构化的记忆卡片
 */
export class RefineryEngine {
    private logger: Logger;

    constructor(
        private ctx: Context,
        private config: RefineryEngineConfig
    ) {
        this.logger = ctx[Services.Logger].getLogger("[记忆提炼引擎]");
    }

    /**
     * 从对话片段提炼记忆卡片
     */
    async refineSegment(segment: DialogueSegment): Promise<MemoryCard[]> {
        try {
            if (!this.config.autoRefineEnabled) {
                this.logger.debug("自动提炼已禁用，跳过片段处理");
                return [];
            }

            if (segment.messages.length < this.config.refineThreshold) {
                this.logger.debug(`片段 ${segment.id} 长度不足提炼阈值，跳过`);
                return [];
            }

            this.logger.debug(`开始提炼片段 ${segment.id}`);

            // 分析对话内容
            const analysisResult = await this.analyzeDialogue(segment);

            // 生成记忆卡片
            const cards = await this.generateMemoryCards(segment, analysisResult);

            this.logger.debug(`从片段 ${segment.id} 提炼出 ${cards.length} 张记忆卡片`);
            return cards;
        } catch (error) {
            this.logger.error(`提炼片段失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 分析对话内容
     */
    private async analyzeDialogue(segment: DialogueSegment): Promise<any> {
        // TODO: 使用LLM分析对话内容，提取关键信息
        // 这里先返回一个简单的分析结果
        const userMessages = segment.messages.filter(m => m.isFromUser);
        const keywords = this.extractKeywords(userMessages.map(m => m.content).join(" "));

        return {
            keywords,
            topics: this.identifyTopics(userMessages),
            facts: this.extractFacts(userMessages),
            preferences: this.extractPreferences(userMessages),
            events: this.extractEvents(userMessages)
        };
    }

    /**
     * 生成记忆卡片
     */
    private async generateMemoryCards(segment: DialogueSegment, analysis: any): Promise<MemoryCard[]> {
        const cards: MemoryCard[] = [];
        const now = new Date().toISOString();

        // 生成事实卡片
        for (const fact of analysis.facts) {
            cards.push({
                card_id: generateCardId(),
                type: "Fact",
                content: fact.content,
                keywords: fact.keywords,
                source_message_ids: fact.messageIds,
                created_at: now,
                last_accessed: now,
                importance: this.calculateImportance(fact),
                state: "Active"
            });
        }

        // 生成偏好卡片
        for (const preference of analysis.preferences) {
            cards.push({
                card_id: generateCardId(),
                type: "Preference",
                content: preference.content,
                keywords: preference.keywords,
                source_message_ids: preference.messageIds,
                created_at: now,
                last_accessed: now,
                importance: this.calculateImportance(preference),
                state: "Active"
            });
        }

        // 生成事件卡片
        for (const event of analysis.events) {
            cards.push({
                card_id: generateCardId(),
                type: "Event",
                content: event.content,
                keywords: event.keywords,
                source_message_ids: event.messageIds,
                created_at: now,
                last_accessed: now,
                importance: this.calculateImportance(event),
                state: "Active",
                trigger: event.trigger,
                followUp: event.followUp
            });
        }

        return cards;
    }

    /**
     * 提取关键词
     */
    private extractKeywords(text: string): string[] {
        // 简单的关键词提取逻辑
        const words = text.toLowerCase()
            .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 1);

        // 去重并返回
        return [...new Set(words)].slice(0, 10);
    }

    /**
     * 识别话题
     */
    private identifyTopics(messages: any[]): string[] {
        // TODO: 实现话题识别逻辑
        return [];
    }

    /**
     * 提取事实
     */
    private extractFacts(messages: any[]): any[] {
        // TODO: 实现事实提取逻辑
        return [];
    }

    /**
     * 提取偏好
     */
    private extractPreferences(messages: any[]): any[] {
        // TODO: 实现偏好提取逻辑
        return [];
    }

    /**
     * 提取事件
     */
    private extractEvents(messages: any[]): any[] {
        // TODO: 实现事件提取逻辑
        return [];
    }

    /**
     * 计算重要性分数
     */
    private calculateImportance(item: any): number {
        // TODO: 实现重要性计算逻辑
        return Math.min(0.5, this.config.maxImportanceScore);
    }
}

/**
 * 情境触发器引擎配置
 */
export interface ContextTriggerEngineConfig {
    /** 是否启用触发器检查 */
    enabled: boolean;
    /** 检查间隔（毫秒） */
    checkIntervalMs: number;
    /** 最大触发结果数量 */
    maxTriggerResults: number;
}

/**
 * 情境触发器引擎
 * 负责在每次对话交互前检查并激活符合条件的触发器
 */
export class ContextTriggerEngine {
    private logger: Logger;

    constructor(
        private ctx: Context,
        private config: ContextTriggerEngineConfig
    ) {
        this.logger = ctx[Services.Logger].getLogger("[情境触发器引擎]");
    }

    /**
     * 检查用户记忆中的触发器
     */
    async checkTriggers(
        userMemory: UserMemory,
        messageContent: string,
        currentTime: Date = new Date()
    ): Promise<TriggerCheckResult[]> {
        try {
            if (!this.config.enabled) {
                return [];
            }

            const results: TriggerCheckResult[] = [];

            for (const card of userMemory.memory_cards) {
                if (!card.trigger || card.state === "Archived") {
                    continue;
                }

                const triggerResult = this.checkSingleTrigger(card, messageContent, currentTime);
                if (triggerResult.triggered) {
                    results.push(triggerResult);
                }

                // 限制结果数量
                if (results.length >= this.config.maxTriggerResults) {
                    break;
                }
            }

            if (results.length > 0) {
                this.logger.debug(`检查到 ${results.length} 个触发器被激活`);
            }

            return results;
        } catch (error) {
            this.logger.error(`检查触发器失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 检查单个触发器
     */
    private checkSingleTrigger(
        card: MemoryCard,
        messageContent: string,
        currentTime: Date
    ): TriggerCheckResult {
        let triggered = false;
        let reason = "";

        switch (card.trigger!.type) {
            case "Time":
                triggered = isTimeTriggerActive(card.trigger, currentTime);
                if (triggered) {
                    reason = `时间触发器激活: ${card.trigger.description || "时间条件满足"}`;
                }
                break;

            case "Context":
                triggered = isContextTriggerActive(card.trigger, messageContent);
                if (triggered) {
                    reason = `上下文触发器激活: 检测到关键词 [${card.trigger.contextKeywords.join(", ")}]`;
                }
                break;

            case "TimeAndContext":
                triggered = isTimeAndContextTriggerActive(card.trigger, messageContent, currentTime);
                if (triggered) {
                    reason = `时间和上下文复合触发器激活: 时间和关键词条件都满足`;
                }
                break;

            case "EventCompletion":
                // 这种类型的触发器需要额外的上下文信息，在这里暂时跳过
                // 应该在更高层的服务中处理
                break;
        }

        return {
            triggered,
            memoryCard: triggered ? card : undefined,
            reason: triggered ? reason : undefined,
            prompt: triggered ? this.generateTriggerPrompt(card, reason) : undefined
        };
    }

    /**
     * 生成触发器提示内容
     */
    private generateTriggerPrompt(card: MemoryCard, reason: string): string {
        const basePrompt = `[记忆触发] ${reason}\n相关记忆: ${card.content}`;

        switch (card.type) {
            case "Event":
                return `${basePrompt}\n建议: 根据这个事件记忆，主动关心用户的进展或状态。`;
            case "Preference":
                return `${basePrompt}\n建议: 根据用户的偏好调整回应，展现出对用户喜好的了解。`;
            case "Fact":
                return `${basePrompt}\n建议: 利用这个事实信息，让对话更加个性化和相关。`;
            case "Goal":
                return `${basePrompt}\n建议: 关注用户的目标进展，提供相关的支持或建议。`;
            case "Relationship":
                return `${basePrompt}\n建议: 考虑用户的人际关系背景，调整对话的语调和内容。`;
            default:
                return `${basePrompt}\n建议: 根据这个记忆信息调整回应，展现出对用户的关心和了解。`;
        }
    }

    /**
     * 批量检查多个用户的触发器
     */
    async batchCheckTriggers(
        userMemories: UserMemory[],
        messageContent: string,
        currentTime: Date = new Date()
    ): Promise<Map<string, TriggerCheckResult[]>> {
        const results = new Map<string, TriggerCheckResult[]>();

        for (const userMemory of userMemories) {
            const userResults = await this.checkTriggers(userMemory, messageContent, currentTime);
            if (userResults.length > 0) {
                results.set(userMemory.user_id, userResults);
            }
        }

        return results;
    }
}
