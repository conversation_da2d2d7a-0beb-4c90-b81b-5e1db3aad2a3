import { Context, ForkScope, h, Service, sleep } from "koishi";
import { AgentCore } from "./agent";
import { ConfiguratorService } from "./commands/configurator";
import { Config } from "./config";
import {
    ImageService,
    LoggerService,
    MemoryService,
    ModelService,
    PromptService,
    TableName,
    ToolService,
    WorldStateService,
} from "./services";
import { formatDate, isNotEmpty } from "./shared";
import { writeFile } from "fs/promises";

declare module "koishi" {
    interface Context {
        yesimbot: YesImBot;
    }
}

export default class YesImBot extends Service<Config> {
    static readonly Config = Config;
    static readonly inject = {
        required: ["console", "database"],
    };
    static readonly name = "yesimbot";
    static readonly usage = `"Yes! I'm Bot!" 是一个能让你的机器人激活灵魂的插件。\n
使用请阅读 [Github README](https://github.com/HydroGest/YesImBot/blob/main/readme.md)，推荐使用 [GPTGOD](https://gptgod.online/#/register?invite_code=envrd6lsla9nydtipzrbvid2r) 提供的 llama-3.1-405b 模型以获得最高性价比。\n
官方交流 & 测试群：[857518324](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=k3O5_1kNFJMERGxBOj1ci43jHvLvfru9&authKey=TkOxmhIa6kEQxULtJ0oMVU9FxoY2XNiA%2B7bQ4K%2FNx5%2F8C8ToakYZeDnQjL%2B31Rx%2B&noverify=0&group_code=857518324)`;
    constructor(ctx: Context, config: Config) {
        super(ctx, "yesimbot", true);

        try {
            // 注册日志服务
            const loggerService = ctx.plugin(LoggerService, config.system.logging);

            // 注册图片服务
            const imageService = ctx.plugin(ImageService, config.imageService);

            // 注册提示词管理器
            const promptService = ctx.plugin(PromptService, {});

            // 注册工具管理器
            const toolService = ctx.plugin(ToolService, { ...config.capabilities.tools, system: config.system });

            // 注册模型服务
            const modelService = ctx.plugin(ModelService, { ...config.modelService, system: config.system });

            // 注册记忆管理层
            const memoryService = ctx.plugin(MemoryService, { ...config.capabilities.memory, system: config.system });

            // 转换 allowedChannelGroups 为 Set
            const allowedChannels: Set<string> = new Set();

            for (const channelGroup of config.agentBehavior.arousal.allowedChannelGroups) {
                for (const channel of channelGroup) {
                    allowedChannels.add(`${channel.platform}:${channel.id}`);
                }
            }

            // 注册 WorldState 服务
            const worldStateService = ctx.plugin(WorldStateService, {
                ...config.capabilities.history,
                allowedChannels,
                system: config.system,
            });

            const agentCore = ctx.plugin(AgentCore, { ...config.agentBehavior, system: config.system });

            const services = [
                loggerService,
                imageService,
                promptService,
                toolService,
                modelService,
                memoryService,
                worldStateService,
                agentCore,
            ];

            waitForServices(services).then(async () => {
                this.ctx.logger.info("所有服务已就绪");
                this.ctx.logger.info(`Version: ${require("../package.json").version}`);

                const messages = await ctx.database
                    .select(TableName.Messages)
                    .where({ "sender.id": "1798037526" })
                    .orderBy("timestamp", "asc")
                    .execute();

                const text = messages
                    .map((m) => {
                        const allowedTypes = ["text", "at"];
                        const contentText = h
                            .parse(m.content)
                            .filter((el) => allowedTypes.includes(el.type))
                            .map((el) => el.toString())
                            .join("")
                            .trim()
                            .replace(/\n/g, " ");
                        if (!contentText) return null;
                        return `[${formatDate(m.timestamp, "YYYY-MM-DD HH:mm:ss")}] ${contentText}`;
                    })
                    .filter(isNotEmpty)
                    .join("\n");

                await writeFile("messages.txt", text);
            });
        } catch (error) {
            this.ctx.logger.error("初始化时发生错误:", error.message);
            this.ctx.logger.error(error.stack);
        }

        ctx.plugin(ConfiguratorService, config);
    }
}

async function waitForServices(services: ForkScope[]) {
    await sleep(1000);
    return new Promise<void>((resolve) => {
        const check = () => {
            if (services.every((service) => service.ready)) {
                resolve();
            } else {
                setTimeout(check, 100);
            }
        };
        check();
    });
}
