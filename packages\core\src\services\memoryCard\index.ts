/**
 * The main document structure for a user's memory.
 * Stored in a document database with user_id as the primary key.
 */
export interface UserMemory {
    /** The unique identifier for the user. */
    user_id: string;

    /** A summary profile of the user, generated and updated by the LLM. */
    profile: UserProfile;

    /** A collection of individual memory units. */
    memory_cards: MemoryCard[];
}

/**
 * User's profile containing summarized information.
 */
export interface UserProfile {
    /** The user's name or nickname. */
    name: string;

    /** AI-inferred personality traits or roles. e.g., "Tech Enthusiast", "Student". */
    personas: string[];

    /** A concise, narrative summary of the user. */
    summary: string;
}

/**
 * Represents a single, atomic piece of memory.
 */
export interface MemoryCard {
    /** Unique identifier for the memory card. */
    card_id: string;

    /** The type of memory. */
    type: "Fact" | "Event" | "Preference" | "Goal" | "Relationship";

    /** The core content of the memory, as a natural language string. */
    content: string;

    /** Keywords for fast retrieval and indexing. */
    keywords: string[];

    /** The IDs of the source messages that led to this memory's creation. */
    source_message_ids: string[];

    /** Timestamp of the memory's creation. */
    created_at: string; // ISO 8601 format

    /** Timestamp of the last time this memory was accessed or used. */
    last_accessed: string; // ISO 8601 format

    /**
     * The importance score (0.0 to 1.0) of the memory, used for retrieval ranking and forgetting mechanism.
     * Can be assigned by the LLM during creation.
     */
    importance: number;

    /**
     * The current state of the memory, crucial for the forgetting mechanism and state-based triggers.
     * - Active: A relevant, current memory.
     * - Fading: An older, less accessed memory, candidate for archival.
     * - Archived: A concluded or outdated memory, kept for long-term reference but excluded from most active searches.
     */
    state: "Active" | "Fading" | "Archived";

    /** Optional. Defines conditions under which this memory should be proactively brought up. */
    trigger?: Trigger;

    /** Optional. Defines a follow-up action to be taken after the memory's event has concluded. */
    followUp?: FollowUp;
}

// =================================================================
// TRIGGER DEFINITIONS
// =================================================================

/** The type of trigger condition. */
export type TriggerType = "Time" | "Context" | "TimeAndContext" | "EventCompletion";

/** Base interface for all triggers. */
export interface BaseTrigger {
    type: TriggerType;
    /** A description of what this trigger is for, used for logging or debugging. */
    description?: string;
}

/** Triggers at a specific time or within a time range. */
export interface TimeTrigger extends BaseTrigger {
    type: "Time";
    /** The start date/time for the trigger window (ISO 8601). */
    startDate: string;
    /** Optional end date/time for the trigger window (ISO 8601). */
    endDate?: string;
}

/** Triggers based on keywords in the conversation. */
export interface ContextTrigger extends BaseTrigger {
    type: "Context";
    /** Keywords that will activate the trigger if they appear in the user's message. */
    contextKeywords: string[];
}

/** Triggers based on a combination of time and context. */
export interface TimeAndContextTrigger extends BaseTrigger {
    type: "TimeAndContext";
    /** The start date/time for the trigger window (ISO 8601). */
    startDate: string;
    /** The end date/time for the trigger window (ISO 8601). */
    endDate: string;
    /** Keywords that will activate the trigger within the specified time window. */
    contextKeywords: string[];
}

/** Triggers when another memory card's state changes. */
export interface EventCompletionTrigger extends BaseTrigger {
    type: "EventCompletion";
    /** The ID of the memory card to monitor. */
    targetCardId: string;
    /** The state of the target card that will activate this trigger. */
    targetState: MemoryCard["state"];
}

/** A union type for any possible Trigger object. */
export type Trigger = TimeTrigger | ContextTrigger | TimeAndContextTrigger | EventCompletionTrigger;

// =================================================================
// FOLLOW-UP DEFINITIONS
// =================================================================

/** The type of follow-up action. */
export type FollowUpType = "TimeDelayed" | "OnStateChange";

/** Base interface for all follow-ups. */
export interface BaseFollowUp {
    type: FollowUpType;
    /** * The prompt or instruction for the LLM on what to do for the follow-up.
     * e.g., "Ask the user about their PMP exam results."
     */
    prompt: string;
}

/** Executes a follow-up after a certain delay. The delay starts when the parent MemoryCard is archived. */
export interface TimeDelayedFollowUp extends BaseFollowUp {
    type: "TimeDelayed";
    /** The delay in days before the follow-up should be triggered. */
    delayDays: number;
}

/** Executes a follow-up immediately when the parent MemoryCard's state changes to a specific value. */
export interface OnStateChangeFollowUp extends BaseFollowUp {
    type: "OnStateChange";
    /** The state that will trigger this follow-up. */
    triggerState: MemoryCard["state"];
}

/** A union type for any possible FollowUp object. */
export type FollowUp = TimeDelayedFollowUp | OnStateChangeFollowUp;


class 