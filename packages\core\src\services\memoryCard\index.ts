/**
 * The main document structure for a user's memory.
 * Stored in a document database with user_id as the primary key.
 */
export interface UserMemory {
    /** The unique identifier for the user. */
    user_id: string;

    /** A summary profile of the user, generated and updated by the LLM. */
    profile: UserProfile;

    /** A collection of individual memory units. */
    memory_cards: MemoryCard[];
}

/**
 * User's profile containing summarized information.
 */
export interface UserProfile {
    /** The user's name or nickname. */
    name: string;

    /** AI-inferred personality traits or roles. e.g., "Tech Enthusiast", "Student". */
    personas: string[];

    /** A concise, narrative summary of the user. */
    summary: string;
}

/**
 * Represents a single, atomic piece of memory.
 */
export interface MemoryCard {
    /** Unique identifier for the memory card. */
    card_id: string;

    /** The type of memory. */
    type: "Fact" | "Event" | "Preference" | "Goal" | "Relationship";

    /** The core content of the memory, as a natural language string. */
    content: string;

    /** Keywords for fast retrieval and indexing. */
    keywords: string[];

    /** The IDs of the source messages that led to this memory's creation. */
    source_message_ids: string[];

    /** Timestamp of the memory's creation. */
    created_at: string; // ISO 8601 format

    /** Timestamp of the last time this memory was accessed or used. */
    last_accessed: string; // ISO 8601 format

    /**
     * The importance score (0.0 to 1.0) of the memory, used for retrieval ranking and forgetting mechanism.
     * Can be assigned by the LLM during creation.
     */
    importance: number;

    /**
     * The current state of the memory, crucial for the forgetting mechanism and state-based triggers.
     * - Active: A relevant, current memory.
     * - Fading: An older, less accessed memory, candidate for archival.
     * - Archived: A concluded or outdated memory, kept for long-term reference but excluded from most active searches.
     */
    state: "Active" | "Fading" | "Archived";

    /** Optional. Defines conditions under which this memory should be proactively brought up. */
    trigger?: Trigger;

    /** Optional. Defines a follow-up action to be taken after the memory's event has concluded. */
    followUp?: FollowUp;
}

// =================================================================
// TRIGGER DEFINITIONS
// =================================================================

/** The type of trigger condition. */
export type TriggerType = "Time" | "Context" | "TimeAndContext" | "EventCompletion";

/** Base interface for all triggers. */
export interface BaseTrigger {
    type: TriggerType;
    /** A description of what this trigger is for, used for logging or debugging. */
    description?: string;
}

/** Triggers at a specific time or within a time range. */
export interface TimeTrigger extends BaseTrigger {
    type: "Time";
    /** The start date/time for the trigger window (ISO 8601). */
    startDate: string;
    /** Optional end date/time for the trigger window (ISO 8601). */
    endDate?: string;
}

/** Triggers based on keywords in the conversation. */
export interface ContextTrigger extends BaseTrigger {
    type: "Context";
    /** Keywords that will activate the trigger if they appear in the user's message. */
    contextKeywords: string[];
}

/** Triggers based on a combination of time and context. */
export interface TimeAndContextTrigger extends BaseTrigger {
    type: "TimeAndContext";
    /** The start date/time for the trigger window (ISO 8601). */
    startDate: string;
    /** The end date/time for the trigger window (ISO 8601). */
    endDate: string;
    /** Keywords that will activate the trigger within the specified time window. */
    contextKeywords: string[];
}

/** Triggers when another memory card's state changes. */
export interface EventCompletionTrigger extends BaseTrigger {
    type: "EventCompletion";
    /** The ID of the memory card to monitor. */
    targetCardId: string;
    /** The state of the target card that will activate this trigger. */
    targetState: MemoryCard["state"];
}

/** A union type for any possible Trigger object. */
export type Trigger = TimeTrigger | ContextTrigger | TimeAndContextTrigger | EventCompletionTrigger;

// =================================================================
// FOLLOW-UP DEFINITIONS
// =================================================================

/** The type of follow-up action. */
export type FollowUpType = "TimeDelayed" | "OnStateChange";

/** Base interface for all follow-ups. */
export interface BaseFollowUp {
    type: FollowUpType;
    /** * The prompt or instruction for the LLM on what to do for the follow-up.
     * e.g., "Ask the user about their PMP exam results."
     */
    prompt: string;
}

/** Executes a follow-up after a certain delay. The delay starts when the parent MemoryCard is archived. */
export interface TimeDelayedFollowUp extends BaseFollowUp {
    type: "TimeDelayed";
    /** The delay in days before the follow-up should be triggered. */
    delayDays: number;
}

/** Executes a follow-up immediately when the parent MemoryCard's state changes to a specific value. */
export interface OnStateChangeFollowUp extends BaseFollowUp {
    type: "OnStateChange";
    /** The state that will trigger this follow-up. */
    triggerState: MemoryCard["state"];
}

/** A union type for any possible FollowUp object. */
export type FollowUp = TimeDelayedFollowUp | OnStateChangeFollowUp;

// =================================================================
// UTILITY TYPES AND INTERFACES
// =================================================================

/**
 * 记忆卡片操作结果
 */
export interface MemoryCardOperationResult<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    code?: string;
}

/**
 * 记忆卡片搜索选项
 */
export interface MemoryCardSearchOptions {
    /** 用户ID */
    userId: string;
    /** 关键词搜索 */
    keywords?: string[];
    /** 记忆卡片类型过滤 */
    types?: MemoryCard["type"][];
    /** 状态过滤 */
    states?: MemoryCard["state"][];
    /** 重要性阈值 */
    minImportance?: number;
    /** 时间范围过滤 */
    timeRange?: {
        start?: string; // ISO 8601
        end?: string; // ISO 8601
    };
    /** 结果数量限制 */
    limit?: number;
    /** 排序方式 */
    sortBy?: "importance" | "created_at" | "last_accessed";
    /** 排序方向 */
    sortOrder?: "asc" | "desc";
}

/**
 * 触发器检查结果
 */
export interface TriggerCheckResult {
    /** 是否被触发 */
    triggered: boolean;
    /** 触发的记忆卡片 */
    memoryCard?: MemoryCard;
    /** 触发原因描述 */
    reason?: string;
    /** 生成的提示内容 */
    prompt?: string;
}

/**
 * 后续追踪任务
 */
export interface FollowUpTask {
    /** 任务ID */
    id: string;
    /** 关联的记忆卡片ID */
    cardId: string;
    /** 用户ID */
    userId: string;
    /** 任务类型 */
    type: FollowUpType;
    /** 提示内容 */
    prompt: string;
    /** 计划执行时间 */
    scheduledAt: string; // ISO 8601
    /** 创建时间 */
    createdAt: string; // ISO 8601
    /** 任务状态 */
    status: "pending" | "completed" | "cancelled";
}

/**
 * 记忆简报
 */
export interface MemoryBrief {
    /** 用户ID */
    userId: string;
    /** 触发的记忆卡片 */
    triggeredCards: MemoryCard[];
    /** 相关的记忆卡片 */
    relevantCards: MemoryCard[];
    /** 待处理的后续追踪任务 */
    pendingFollowUps: FollowUpTask[];
    /** 生成的提示内容 */
    prompts: string[];
    /** 生成时间 */
    generatedAt: string; // ISO 8601
}

// =================================================================
// UTILITY FUNCTIONS
// =================================================================

/**
 * 生成记忆卡片ID
 */
export function generateCardId(): string {
    return `card_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 生成后续追踪任务ID
 */
export function generateFollowUpTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 检查时间触发器是否激活
 */
export function isTimeTriggerActive(trigger: TimeTrigger, currentTime: Date = new Date()): boolean {
    const startDate = new Date(trigger.startDate);
    const endDate = trigger.endDate ? new Date(trigger.endDate) : null;

    if (currentTime < startDate) {
        return false;
    }

    if (endDate && currentTime > endDate) {
        return false;
    }

    return true;
}

/**
 * 检查上下文触发器是否激活
 */
export function isContextTriggerActive(trigger: ContextTrigger, messageContent: string): boolean {
    const content = messageContent.toLowerCase();
    return trigger.contextKeywords.some(keyword =>
        content.includes(keyword.toLowerCase())
    );
}

/**
 * 检查时间和上下文复合触发器是否激活
 */
export function isTimeAndContextTriggerActive(
    trigger: TimeAndContextTrigger,
    messageContent: string,
    currentTime: Date = new Date()
): boolean {
    // 首先检查时间条件
    const startDate = new Date(trigger.startDate);
    const endDate = new Date(trigger.endDate);

    if (currentTime < startDate || currentTime > endDate) {
        return false;
    }

    // 然后检查上下文条件
    const content = messageContent.toLowerCase();
    return trigger.contextKeywords.some(keyword =>
        content.includes(keyword.toLowerCase())
    );
}

/**
 * 计算后续追踪任务的执行时间
 */
export function calculateFollowUpScheduleTime(
    followUp: TimeDelayedFollowUp,
    cardArchivedAt: Date
): Date {
    const scheduleTime = new Date(cardArchivedAt);
    scheduleTime.setDate(scheduleTime.getDate() + followUp.delayDays);
    return scheduleTime;
}

/**
 * 验证记忆卡片数据的完整性
 */
export function validateMemoryCard(card: Partial<MemoryCard>): MemoryCardOperationResult<MemoryCard> {
    const errors: string[] = [];

    if (!card.card_id) {
        errors.push("缺少卡片ID");
    }

    if (!card.type) {
        errors.push("缺少卡片类型");
    }

    if (!card.content || card.content.trim().length === 0) {
        errors.push("缺少卡片内容");
    }

    if (!card.keywords || card.keywords.length === 0) {
        errors.push("缺少关键词");
    }

    if (typeof card.importance !== "number" || card.importance < 0 || card.importance > 1) {
        errors.push("重要性分数必须在0-1之间");
    }

    if (!card.state) {
        errors.push("缺少卡片状态");
    }

    if (errors.length > 0) {
        return {
            success: false,
            error: `记忆卡片验证失败: ${errors.join(", ")}`,
            code: "VALIDATION_ERROR"
        };
    }

    return {
        success: true,
        data: card as MemoryCard
    };
}

/**
 * 创建默认的用户记忆结构
 */
export function createDefaultUserMemory(userId: string, userName?: string): UserMemory {
    return {
        user_id: userId,
        profile: {
            name: userName || "未知用户",
            personas: [],
            summary: "新用户，暂无详细信息。"
        },
        memory_cards: []
    };
}

// =================================================================
// SERVICE EXPORTS
// =================================================================

export * from "./service";
export * from "./config";
